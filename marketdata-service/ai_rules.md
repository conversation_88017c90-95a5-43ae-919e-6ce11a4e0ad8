# 🤖 AI Rules for This Codebase

## 🧭 Context

This project follows **Hexagonal Architecture (Ports & Adapters)** using **FastAPI**.  
All generated or modified code must comply with these architectural, design, and clean-code rules.

---

## 1. Architectural Rules

### 1.1 Folder Structure

app/
├── api/ # Entry points (FastAPI routers)
├── core/ # Domain logic (entities, repositories, use cases)
├── infrastructure/ # Adapters (DB, external APIs, config)
└── shared/ # Common schemas, exceptions, utilities


### 1.2 Dependency Direction
- Dependencies flow **inward only**:
  - `api` → `core` (via use cases)
  - `infrastructure` → `core` (via interfaces)
- Never the reverse.
- The domain layer (`core`) must remain **framework-agnostic** and free of any FastAPI or DB code.

### 1.3 Entry Points
- Only FastAPI routers in `app/api/routers` handle HTTP requests.
- Routers delegate to **use cases**; no business logic belongs in routes.

### 1.4 Dependency Injection
- Inject dependencies through constructors (e.g., `use_case = CreateUserUseCase(repo)`).
- Never instantiate adapters or infrastructure components inside domain classes.

---

## 2. Domain Layer Rules (`app/core`)

### 2.1 Entities
- Use **dataclasses** or plain Python classes.
- Must not depend on Pydantic or FastAPI.
- Represent business concepts and rules only.

### 2.2 Repository Interfaces (Ports)
- Define abstract base classes (`ABC`) for data access.
- Method names should reflect domain meaning (e.g., `get_by_email`, not `fetch_row`).

### 2.3 Use Cases (Application Services)
- Encapsulate business logic and orchestrate repository calls.
- Depend only on repository **interfaces**, never concrete implementations.
- Raise domain exceptions, not HTTP exceptions.

---

## 3. Infrastructure Layer Rules (`app/infrastructure`)

- Implements the **ports** defined in the domain layer.
- Handles I/O (database, APIs, caching, etc.).
- Contains **no domain logic**.
- Keeps configuration and environment loading here.
- Avoid circular imports between infrastructure and core.

---

## 4. API Layer Rules (`app/api`)

### 4.1 Routers
- One router per aggregate or bounded context (`user_router.py`, `order_router.py`, etc.).
- Use Pydantic models for request and response validation only.
- Convert between Pydantic schemas ↔ domain entities.

### 4.2 Exception Handling
- HTTP error translation happens only here.
- Never raise `HTTPException` from the domain or infrastructure layers.

---

## 5. Cross-Layer Rules

### 5.1 Data Models
- **Pydantic** is allowed only in the API and `shared/schemas` layers.
- Domain must remain unaware of Pydantic or FastAPI.

### 5.2 Logging & Error Handling
- Domain exceptions should be meaningful and isolated.
- Infrastructure handles logging.
- API layer translates domain exceptions to HTTP responses.

### 5.3 Naming Conventions

| Concept | Suffix | Example |
|----------|---------|----------|
| Entity | `*` | `User` |
| Repository Interface | `Repository` | `UserRepository` |
| Repository Implementation | `RepositoryImpl` | `UserRepositoryImpl` |
| Use Case | `UseCase` | `CreateUserUseCase` |
| Router | `router.py` | `user_router.py` |

---

## 6. SOLID Principles

| Principle | Description |
|------------|-------------|
| **S**ingle Responsibility | One reason to change per class/module. |
| **O**pen/Closed | Open for extension, closed for modification. |
| **L**iskov Substitution | Subtypes must substitute their base types. |
| **I**nterface Segregation | Prefer many small, focused interfaces. |
| **D**ependency Inversion | Depend on abstractions, not concretions. |

---

## 7. Clean Code Practices

1. Prefer **clear names** over short ones (`create_user`, not `cu`).
2. Functions should be **small** and **single-purpose**.
3. Avoid **side effects** unless explicit.
4. No magic constants — use named constants or enums.
5. Catch and raise **domain-specific exceptions**.
6. Always write **docstrings and type hints**.
7. Add **unit tests** for every use case and repository.
8. Avoid **tight coupling** and **global state**.
9. No business logic in routers or infrastructure adapters.
10. Maintain **immutability** where practical.

---

## 8. Testing Rules

- Unit-test use cases with mock or fake repositories.
- Integration tests use `TestClient` for HTTP testing.
- Tests should not depend on real infrastructure (DB, APIs) unless explicitly marked as integration.
- Each test must assert expected domain behavior, not HTTP responses alone.

---

## 9. Code Style

- Follow **PEP 8** and **Black** formatting.
- Use **type hints** consistently.
- Run **mypy** for static type checking.
- Lint with **ruff** or **flake8**.
- Keep functions ≤ 20 lines when possible.

---

## 10. AI Agent Behavior Rules

When generating, refactoring, or reviewing code:

1. Respect **layer boundaries** — never mix API, infrastructure, or domain logic.
2. Always depend on **abstractions**, not implementations.
3. Generate **docstrings** and **type hints**.
4. Never expose infrastructure details (e.g., database schemas) to domain or API.
5. Prioritize **testability** and **dependency injection**.
6. Ensure **domain code can run without FastAPI**.
7. Reject “shortcut” or “quick hack” code that violates Hexagonal or SOLID principles.
8. Follow **consistent naming** and **folder placement**.
9. Use **pure functions** and **immutable data** when possible.
10. Suggest improvements that enhance readability, decoupling, and maintainability.

---

## ✅ Summary

All generated code should:
- Be **framework-independent at the core**
- Be **SOLID and testable**
- Be **clear, documented, and maintainable**
- Follow **Hexagonal Architecture** strictly
- Keep **FastAPI**, **Pydantic**, and **DB logic** out of the core domain

> 💡 *“The framework is a detail — the domain is the heart of the system.”*
