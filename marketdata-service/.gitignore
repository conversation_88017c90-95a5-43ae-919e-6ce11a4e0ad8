# -------------------------
# Python & FastAPI
# -------------------------
__pycache__/
*.py[cod]
*$py.class

# Virtual environments
.venv/
venv/
env/
ENV/
.venv/*
venv/*
env/*
ENV/*

# Uvicorn / FastAPI logs
*.log
*.pid

# Local database files (e.g., SQLite)
*.db
*.sqlite3

# -------------------------
# Testing & Coverage
# -------------------------
.coverage
.tox/
.nox/
htmlcov/
.pytest_cache/
.cache/
*.cover
coverage.xml

# -------------------------
# Build & Distribution
# -------------------------
build/
dist/
develop-eggs/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg

# -------------------------
# <PERSON><PERSON>ter / Notebooks
# -------------------------
.ipynb_checkpoints

# -------------------------
# Editors / IDEs
# -------------------------
.vscode/
.idea/
*.swp
*.swo

# -------------------------
# System files
# -------------------------
.DS_Store
Thumbs.db

# -------------------------
# Docker
# -------------------------
# Ignore build artifacts and local configs
docker-compose.override.yml
*.env
.env.local
.env.*.local

# -------------------------
# PyCharm (optional)
# -------------------------
.idea/
*.iml

# -------------------------
# Node / Frontend (if using a SPA or frontend)
# -------------------------
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log

# -------------------------
# Miscellaneous
# -------------------------
*.bak
*.tmp
*.old
