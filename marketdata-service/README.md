# Market Data Service

Vietnamese stock market data and news service with RSS news crawler.

## 🚀 Getting Started

### Prerequisites

- Python 3.10+
- Poetry
- MongoDB 7+
- Docker and Docker Compose (recommended)

### Installation

1. Clone the repository:

```bash
git clone <repository-url>
cd marketdata-service
```

2. Install dependencies:

```bash
poetry install
```

3. Set up environment variables:

```bash
cp .env.example .env
# Edit .env with your MongoDB configuration
```

Required environment variables:

- `MONGO_URI`: MongoDB connection string
- `MONGO_DB_NAME`: Database name (default: market_data)
- `MONGO_ROOT_USERNAME`: MongoDB admin username
- `MONGO_ROOT_PASSWORD`: MongoDB admin password
- `SERVER_PORT`: API server port (default: 8000)

### Running the Application

#### Using Docker Compose (Recommended):

```bash
docker-compose up -d
```

This will start:

- MongoDB instance on port 27017
- Market Data Service API on port 8000

#### Using Poetry:

```bash
# Make sure MongoDB is running
poetry run uvicorn src.main:app --host 0.0.0.0 --port 8000
```

## 🛠️ Development

### Running Tests

```bash
poetry run pytest
```

### Code Formatting

```bash
poetry run poe format
```

### Linting

```bash
poetry run poe lint
```

## 📁 Project Structure

```
marketdata-service/
├── src/
│   ├── api/                    # API layer (FastAPI)
│   │   ├── routers/           # API route handlers
│   │   ├── schemas.py         # Pydantic models
│   │   └── dependencies.py    # Dependency injection
│   ├── core/                   # Core domain layer
│   │   ├── entities/          # Domain entities
│   │   ├── repositories/      # Repository interfaces
│   │   └── usecases/          # Business logic
│   ├── infrastructure/         # Infrastructure layer
│   │   ├── mongo/             # MongoDB implementations
│   │   └── adapter/           # External adapters (RSS, etc.)
│   └── main.py                # Application entry point
├── scripts/                    # Utility scripts
│   ├── seed_data_tickers.py   # Seed ticker data
│   └── crawl_news.py          # Crawl news from RSS
├── tests/                      # Test suite
├── pyproject.toml             # Poetry dependencies
├── docker-compose.yml         # Docker composition
└── README.md                  # This file
```
