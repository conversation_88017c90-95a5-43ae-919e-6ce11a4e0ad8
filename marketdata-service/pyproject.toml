[tool.poetry]
name = "marketdata-service"
version = "0.1.0"
description = "Vietnamese stock market data scraping and storage service"
authors = ["phduchuy <<EMAIL>>"]
readme = "README.md"
packages = [{include = "src"}]

[tool.poetry.dependencies]
python = "^3.10"
fastapi = ">=0.118.0,<0.119.0"
uvicorn = ">=0.37.0,<0.38.0"
motor = ">=3.1.1"
yfinance = ">=0.2.18"
pydantic = ">=1.10.0"
python-dotenv = ">=1.0.0"
pytz = ">=2025.2"
feedparser = ">=6.0.10"
aiohttp = ">=3.9.0"
beautifulsoup4 = ">=4.12.0"
lxml = ">=5.0.0"
vnstock = "^3.2.6"
redis = "^6.4.0"
apscheduler = "^3.11.0"
ta-lib = "^0.6.8"
logging = "^*******"

[tool.poetry.group.dev.dependencies]
pytest = ">=7.0"
pytest-asyncio = ">=0.20"
pytest-cov = ">=4.0"
black = ">=23.0"
ruff = ">=0.1.0"
mypy = ">=1.0"
poethepoet = ">=0.24.0"
httpx = "^0.28.1"

[tool.poe.tasks]
format = "black src/ tests/"
format-check = "black --check src/ tests/"
ruff-check = "ruff check src/ tests/"
ruff-fix = "ruff check --fix src/ tests/"
mypy-check = "mypy src/"
test-unit = "pytest tests/ -v --cov=src --cov-report=html --cov-report=term"
test = "pytest tests/ -v"
lint = ["format-check", "ruff-check", "mypy-check"]

[tool.black]
line-length = 100
target-version = ['py310']
include = '\.pyi?$'

[tool.ruff]
line-length = 100
target-version = "py310"
select = ["E", "F", "I", "N", "W", "UP"]
ignore = ["E501"]

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = false
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
asyncio_mode = "auto"
addopts = "-v --strict-markers"

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
