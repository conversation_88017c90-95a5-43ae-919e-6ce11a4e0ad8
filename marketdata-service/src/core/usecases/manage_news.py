import logging
from datetime import datetime
from typing import List, Optional

from core.entities.news_article import NewsArticle
from core.repositories.news_repository import NewsRepository
from core.exceptions import NotFoundError, RepositoryError


logger = logging.getLogger(__name__)


class SaveNewsArticlesUseCase:
    """Use case for saving news articles to the repository."""

    def __init__(self, repository: NewsRepository) -> None:
        self._repo = repository

    async def execute(self, articles: List[NewsArticle]) -> int:
        if not articles:
            return 0

        logger.info(f"Saving {len(articles)} news articles")
        count = await self._repo.save_articles(articles)
        logger.info(f"Successfully saved {count} news articles")

        return count


class GetRecentNewsUseCase:
    """Use case for retrieving recent news articles."""

    def __init__(self, repository: NewsRepository) -> None:
        self._repo = repository

    async def execute(
        self, limit: int = 100, skip: int = 0, source: Optional[str] = None
    ) -> tuple[List[NewsArticle], int]:
        logger.info(f"Retrieving recent news (limit={limit}, skip={skip})")
        if source:
            articles, total = await self._repo.filter_source_recent(limit, skip, source)
        else:
            articles, total = await self._repo.get_recent(limit, skip)

        return articles, total


class GetNewsBySourceUseCase:
    """Use case for retrieving news articles from a specific source."""

    def __init__(self, repository: NewsRepository) -> None:
        self._repo = repository

    async def execute(self, source: str, limit: int = 100, skip: int = 0) -> List[NewsArticle]:
        logger.info(f"Retrieving news from source: {source} (limit={limit}, skip={skip})")
        articles = await self._repo.get_by_source(source, limit, skip)
        logger.info(f"Retrieved {len(articles)} articles from {source}")
        return articles


class GetNewsByDateRangeUseCase:
    """Use case for retrieving news articles within a date range."""

    def __init__(self, repository: NewsRepository) -> None:
        self._repo = repository

    async def execute(self, start: datetime, end: datetime, limit: int = 1000) -> List[NewsArticle]:
        logger.info(f"Retrieving news from {start} to {end}")
        articles = await self._repo.get_by_date_range(start, end, limit)
        logger.info(f"Retrieved {len(articles)} articles in date range")
        return articles


class SearchNewsUseCase:
    """Use case for searching news articles by keyword."""

    def __init__(self, repository: NewsRepository) -> None:
        self._repo = repository

    async def execute(self, keyword: str, limit: int = 100, skip: int = 0) -> List[NewsArticle]:
        logger.info(f"Searching news for keyword: {keyword} (limit={limit}, skip={skip})")
        articles = await self._repo.search_by_keyword(keyword, limit, skip)
        logger.info(f"Found {len(articles)} articles matching '{keyword}'")
        return articles
