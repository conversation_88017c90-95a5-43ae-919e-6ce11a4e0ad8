from src.infrastructure.adapter.technical_analysis import TechnicalAnalysisAdapter
from src.infrastructure.adapter.vnstock_data import VnStockAdapter

class GetSymbolTechnicalAnalysisUseCase:
    def __init__(self, data_source=None, ta_service=None):
        self.data_source = data_source or VnStockAdapter()
        self.ta_service = ta_service or TechnicalAnalysisAdapter()

    def execute(self, symbol: str, start: str, end: str):
        data = self.data_source.get_history(
            symbol,
            start=start,
            end=end,
        )

        analyzed = self.ta_service.compute(data)
        return analyzed