import logging
from typing import List, Dict

from core.exceptions import RepositoryError
from core.repositories.news_repository import NewsRepository

logger = logging.getLogger(__name__)


class CrawlNewsUseCase:
    def __init__(self, repository: NewsRepository, crawler) -> None:
        self._repo = repository
        self._crawler = crawler

    async def execute(self, feeds: List[Dict[str, str]]) -> Dict[str, any]:
        logger.info(f"Starting news crawl for {len(feeds)} feeds")

        try:
            # Crawl all feeds
            articles_by_source = await self._crawler.crawl_multiple_feeds(feeds)

            total_fetched = 0
            total_saved = 0
            results_by_source = {}

            # Save articles for each source
            for source, articles in articles_by_source.items():
                fetched_count = len(articles)
                total_fetched += fetched_count

                if articles:
                    try:
                        saved_count = await self._repo.save_articles(articles)
                        total_saved += saved_count
                        results_by_source[source] = {
                            "fetched": fetched_count,
                            "saved": saved_count,
                            "status": "success",
                        }
                    except RepositoryError as exc:
                        logger.error(f"Failed to save articles from {source}: {exc}")
                        results_by_source[source] = {
                            "fetched": fetched_count,
                            "saved": 0,
                            "status": "error",
                            "error": str(exc),
                        }
                else:
                    results_by_source[source] = {
                        "fetched": 0,
                        "saved": 0,
                        "status": "no_articles",
                    }

            result = {
                "total_feeds": len(feeds),
                "total_articles_fetched": total_fetched,
                "total_articles_saved": total_saved,
                "by_source": results_by_source,
            }

            logger.info(f"Crawl completed: {total_fetched} fetched, {total_saved} saved")

            return result

        except Exception as exc:
            logger.error(f"News crawl failed: {exc}", exc_info=True)
            raise RepositoryError(f"News crawl failed: {exc}") from exc
        finally:
            await self._crawler.close()
