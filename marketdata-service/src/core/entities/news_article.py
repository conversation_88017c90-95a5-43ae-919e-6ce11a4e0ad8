from dataclasses import dataclass
from datetime import datetime
from typing import Optional, List


@dataclass(frozen=True)
class NewsArticle:
    title: str
    link: str
    description: str
    published_at: datetime
    source: str
    source_name: str
    categories: List[str] = None
    image_url: Optional[str] = None
    guid: Optional[str] = None

    def __post_init__(self) -> None:
        if not self.title or not self.title.strip():
            raise ValueError("Title cannot be empty")
        if not self.link or not self.link.strip():
            raise ValueError("Link cannot be empty")
        if not self.source or not self.source.strip():
            raise ValueError("Source cannot be empty")
        if not self.source_name or not self.source_name.strip():
            raise ValueError("Source name cannot be empty")
        if self.categories is None:
            object.__setattr__(self, "categories", [])
