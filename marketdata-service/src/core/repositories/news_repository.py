from __future__ import annotations

from abc import ABC, abstractmethod
from datetime import datetime
from typing import List, Optional

from core.entities.news_article import NewsArticle


class NewsRepository(ABC):
    @abstractmethod
    async def save_article(self, article: NewsArticle) -> None:
        pass

    @abstractmethod
    async def save_articles(self, articles: List[NewsArticle]) -> int:
        pass

    @abstractmethod
    async def get_by_link(self, link: str) -> Optional[NewsArticle]:
        pass

    @abstractmethod
    async def get_by_source(
        self, source: str, limit: int = 100, skip: int = 0
    ) -> List[NewsArticle]:
        pass

    @abstractmethod
    async def get_recent(self, limit: int = 100, skip: int = 0) -> tuple[List[NewsArticle], int]:
        pass

    @abstractmethod
    async def filter_source_recent(
        self, limit: int = 100, skip: int = 0, source: str = None
    ) -> tuple[List[NewsArticle], int]:
        pass

    @abstractmethod
    async def get_by_date_range(
        self, start: datetime, end: datetime, limit: int = 1000
    ) -> List[NewsArticle]:
        pass

    @abstractmethod
    async def search_by_keyword(
        self, keyword: str, limit: int = 100, skip: int = 0
    ) -> List[NewsArticle]:
        pass

    @abstractmethod
    async def exists_by_link(self, link: str) -> bool:
        pass

    @abstractmethod
    async def delete_old_articles(self, before: datetime) -> int:
        pass

    @abstractmethod
    async def count_by_source(self, source: str) -> int:
        pass
