import pandas as pd
from typing import Any, List, Dict, Optional

class CommonFunction:
    @staticmethod
    def dataframe_to_records(df: Optional[pd.DataFrame]) -> List[Dict[str, Any]]:
        """
        Safely converts a Pandas DataFrame to a list of dicts, handling:
          - Empty or None dataframes
          - NaN/NaT conversion to None
          - Datetime column formatting
        """
        if df is None or df.empty:
            return []

        df = df.copy()

        for col in df.columns:
            if pd.api.types.is_datetime64_any_dtype(df[col]):
                df[col] = df[col].dt.strftime("%Y-%m-%dT%H:%M:%S")

        df = df.where(pd.notnull(df), None)

        return df.to_dict(orient="records")

    @staticmethod
    def safe_to_datetime(value: Any) -> Optional[pd.Timestamp]:
        """
        Safely convert a value to datetime (returns None if invalid).
        """
        try:
            return pd.to_datetime(value)
        except Exception:
            return None

    @staticmethod
    def is_valid_dataframe(df: Any) -> bool:
        """
        Check if the object is a non-empty Pandas DataFrame.
        """
        return isinstance(df, pd.DataFrame) and not df.empty
