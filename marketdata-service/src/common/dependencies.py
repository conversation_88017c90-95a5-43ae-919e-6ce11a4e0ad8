import os

from infrastructure.mongo.mongo_client import get_mongo_client
from infrastructure.mongo.news_repository_impl import MongoNewsRepository
from infrastructure.adapter.rss_crawler import RSSCrawler
from core.repositories.news_repository import NewsRepository


DB_NAME = os.environ.get("MONGO_DB_NAME", "market_data")

async def get_news_repository() -> NewsRepository:
    client = get_mongo_client()
    db = client[DB_NAME]
    return MongoNewsRepository(db)


async def get_rss_crawler() -> RSSCrawler:
    return RSSCrawler(
        max_retries=3,
        retry_delay=1.0,
        timeout=30,
        rate_limit_delay=1.0,
    )
