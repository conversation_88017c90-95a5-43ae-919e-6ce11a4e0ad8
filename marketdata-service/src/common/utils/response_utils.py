import time
from functools import wraps
from typing import Any, Callable, List, Optional, TypeVar
from fastapi import HTTPException
from fastapi.responses import JSONResponse

from src.common.payload.response_models import ResponseBuilder, StandardResponse, ErrorCodes
from core.exceptions import NotFoundError, RepositoryError

F = TypeVar("F", bound=Callable[..., Any])


class ResponseWrapper:
    @staticmethod
    def standardize_response():
        def decorator(func: F) -> F:
            @wraps(func)
            async def wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = await func(*args, **kwargs)
                    return ResponseBuilder.success(data=result)

                except HTTPException as exc:
                    error_response = ResponseBuilder.error(
                        code=_get_error_code_from_status(exc.status_code),
                        message=str(exc.detail),
                    )
                    return JSONResponse(
                        status_code=exc.status_code, content=error_response.model_dump()
                    )

                except NotFoundError as exc:
                    error_response = ResponseBuilder.error(
                        code=ErrorCodes.NOT_FOUND,
                        message=str(exc),
                    )
                    return JSONResponse(status_code=404, content=error_response.model_dump())

                except RepositoryError as exc:
                    error_response = ResponseBuilder.error(
                        code=ErrorCodes.REPOSITORY_ERROR,
                        message=str(exc),
                    )
                    return JSONResponse(status_code=500, content=error_response.model_dump())

                except ValueError as exc:
                    error_response = ResponseBuilder.error(
                        code=ErrorCodes.VALIDATION_ERROR,
                        message=str(exc),
                    )
                    return JSONResponse(status_code=400, content=error_response.model_dump())

                except Exception as exc:
                    error_response = ResponseBuilder.error(
                        code=ErrorCodes.INTERNAL_ERROR,
                        message="An unexpected error occurred",
                        details={"original_error": str(exc)},
                    )
                    return JSONResponse(status_code=500, content=error_response.model_dump())

            return wrapper

        return decorator


def _get_error_code_from_status(status_code: int) -> str:
    mapping = {
        400: ErrorCodes.BAD_REQUEST,
        401: ErrorCodes.AUTHENTICATION_ERROR,
        403: ErrorCodes.AUTHORIZATION_ERROR,
        404: ErrorCodes.NOT_FOUND,
        409: ErrorCodes.CONFLICT_ERROR,
        429: ErrorCodes.RATE_LIMIT_ERROR,
        500: ErrorCodes.INTERNAL_ERROR,
    }
    return mapping.get(status_code, ErrorCodes.INTERNAL_ERROR)


class PaginationHelper:
    @staticmethod
    def create_paginated_response(
        data: List[Any],
        total_count: int,
        page: int,
        page_size: int,
    ) -> StandardResponse:
        return ResponseBuilder.success_list(
            data=data, total_count=total_count, page=page, page_size=page_size
        )

    @staticmethod
    def calculate_pagination_params(limit: int, skip: int, total_count: int) -> tuple[int, int]:
        page_size = limit
        page = (skip // limit) + 1 if limit > 0 else 1
        return page, page_size


def create_success_response(data: Any = None, message: Optional[str] = None) -> StandardResponse:
    if message and data is None:
        return ResponseBuilder.success_message(message)
    return ResponseBuilder.success(data=data)


def create_error_response(
    code: str,
    message: str,
    status_code: int = 500,
    field: Optional[str] = None,
    details: Optional[dict] = None,
) -> JSONResponse:
    error_response = ResponseBuilder.error(code=code, message=message, field=field, details=details)
    return JSONResponse(status_code=status_code, content=error_response.model_dump())


def standardize_response_with_timing(func: F) -> F:
    return ResponseWrapper.standardize_response()(func)
