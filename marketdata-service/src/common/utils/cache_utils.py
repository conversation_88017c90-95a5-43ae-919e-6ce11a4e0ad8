import json
import asyncio
from typing import Callable, Any, Optional, Tuple
from datetime import datetime, date
from decimal import Decimal

from redis.asyncio import Redis

redis = Redis(host="redis", port=6379, decode_responses=True)


def _default_json_serializer(o: Any):
    """JSON serializer for objects not serializable by default json code."""
    if isinstance(o, (datetime, date)):
        return o.isoformat()
    if isinstance(o, Decimal):
        return float(o)
    try:
        from pydantic import BaseModel

        if isinstance(o, BaseModel):
            return o.model_dump()
    except Exception:
        pass
    # numpy scalars / arrays
    try:
        import numpy as np

        if isinstance(o, (np.integer, np.floating)):
            return o.item()
        if isinstance(o, np.ndarray):
            return o.tolist()
    except Exception:
        pass

    # Fallback: let json raise a TypeError with a helpful message
    raise TypeError(f"Object of type {o.__class__.__name__} is not JSON serializable")


class CacheUtils:
    def __init__(self, redis_client: Optional[Redis] = None, default_ttl: int = 600):
        self.redis = redis_client or redis
        self.default_ttl = default_ttl

    async def get_json(self, key: str) -> Optional[Any]:
        cached = await self.redis.get(key)
        if cached:
            try:
                return json.loads(cached)
            except Exception:
                # If cached content isn't valid JSON for any reason, return raw
                return cached
        return None

    async def set_json(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """
        Store a Python object as JSON in Redis.
        Uses a safe default serializer to handle datetimes, decimals, numpy and pydantic models.
        Runs the actual Redis setex in a background task (fire-and-forget).
        """
        ttl = ttl or self.default_ttl
        # Convert to JSON using the default serializer for non-standard types
        json_data = json.dumps(value, default=_default_json_serializer, ensure_ascii=False)
        # Fire-and-forget write to redis so we don't block response time
        asyncio.create_task(self.redis.setex(key, ttl, json_data))

    async def get_or_set(
        self,
        key: str,
        fetch_func: Callable[[], Any],
        ttl: Optional[int] = None,
        transform: Optional[Callable[[Any], Any]] = None,
    ) -> Tuple[Any, bool]:
        """
        Return (data, from_cache).
        - If cached: returns cached data and True.
        - Else: executes fetch_func (sync or async), optionally transform(result), caches it and returns (result, False).
        """
        cached = await self.get_json(key)
        if cached is not None:
            return cached, True

        # execute fetcher (support both sync and async functions)
        result = await fetch_func() if asyncio.iscoroutinefunction(fetch_func) else fetch_func()

        if transform:
            result_to_cache = transform(result)
        else:
            result_to_cache = result

        await self.set_json(key, result_to_cache, ttl)
        return result_to_cache, False

    async def invalidate(self, key: str) -> None:
        """Delete a cache key."""
        await self.redis.delete(key)
