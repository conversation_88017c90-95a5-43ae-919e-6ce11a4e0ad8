from datetime import datetime, timezone
from typing import Any, Dict, Generic, List, Optional, TypeVar
from pydantic import BaseModel, Field
from enum import Enum

T = TypeVar("T")


class ResponseStatus(str, Enum):
    SUCCESS = "success"
    ERROR = "error"


class PaginationMeta(BaseModel):
    total_count: int = Field(..., description="Total number of items available")
    page: int = Field(..., ge=1, description="Current page number (1-based)")
    page_size: int = Field(..., ge=1, description="Number of items per page")
    total_pages: int = Field(..., ge=1, description="Total number of pages")
    has_next: bool = Field(..., description="Whether there is a next page")
    has_previous: bool = Field(..., description="Whether there is a previous page")

    @classmethod
    def create(cls, total_count: int, page: int, page_size: int) -> "PaginationMeta":
        total_pages = max(1, (total_count + page_size - 1) // page_size)
        return cls(
            total_count=total_count,
            page=page,
            page_size=page_size,
            total_pages=total_pages,
            has_next=page < total_pages,
            has_previous=page > 1,
        )


class ResponseMeta(BaseModel):
    timestamp: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc), description="Response timestamp"
    )
    pagination: Optional[PaginationMeta] = Field(
        None, description="Pagination information for list responses"
    )

    class Config:
        extra = "allow"
        json_encoders = {datetime: lambda v: v.isoformat()}


class StandardResponse(BaseModel, Generic[T]):
    status: ResponseStatus = Field(..., description="Response status indicator")
    data: Optional[T] = Field(None, description="Response payload")
    meta: ResponseMeta = Field(default_factory=ResponseMeta, description="Response metadata")

    class Config:
        use_enum_values = True
        json_encoders = {datetime: lambda v: v.isoformat()}


class ErrorDetail(BaseModel):
    code: str = Field(..., description="Error code")
    message: str = Field(..., description="Human-readable error message")
    field: Optional[str] = Field(None, description="Field name if validation error")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")


class ErrorResponse(BaseModel):
    status: ResponseStatus = Field(ResponseStatus.ERROR, description="Response status indicator")
    data: None = Field(None, description="Always null for error responses")
    meta: ResponseMeta = Field(default_factory=ResponseMeta, description="Response metadata")
    error: ErrorDetail = Field(..., description="Error information")

    class Config:
        use_enum_values = True
        json_encoders = {datetime: lambda v: v.isoformat()}


SuccessResponse = StandardResponse[T]
ListResponse = StandardResponse[List[T]]
DictResponse = StandardResponse[Dict[str, Any]]
MessageResponse = StandardResponse[Dict[str, str]]


class ResponseBuilder:
    @staticmethod
    def success(
        data: Any = None,
    ) -> StandardResponse:
        meta = ResponseMeta()
        return StandardResponse(status=ResponseStatus.SUCCESS, data=data, meta=meta)

    @staticmethod
    def success_list(
        data: List[Any],
        total_count: int,
        page: int = 1,
        page_size: Optional[int] = None,
    ) -> StandardResponse:
        if page_size is None:
            page_size = len(data)

        pagination = PaginationMeta.create(total_count, page, page_size)
        meta = ResponseMeta(pagination=pagination)

        return StandardResponse(status=ResponseStatus.SUCCESS, data=data, meta=meta)

    @staticmethod
    def success_message(message: str) -> StandardResponse:
        return ResponseBuilder.success(data={"message": message})

    @staticmethod
    def error(
        code: str,
        message: str,
        field: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
    ) -> ErrorResponse:
        meta = ResponseMeta()

        error_detail = ErrorDetail(code=code, message=message, field=field, details=details)
        return ErrorResponse(meta=meta, error=error_detail)


class ErrorCodes:
    VALIDATION_ERROR = "VALIDATION_ERROR"
    NOT_FOUND = "NOT_FOUND"
    INTERNAL_ERROR = "INTERNAL_ERROR"
    REPOSITORY_ERROR = "REPOSITORY_ERROR"
    EXTERNAL_SERVICE_ERROR = "EXTERNAL_SERVICE_ERROR"
    AUTHENTICATION_ERROR = "AUTHENTICATION_ERROR"
    AUTHORIZATION_ERROR = "AUTHORIZATION_ERROR"
    RATE_LIMIT_ERROR = "RATE_LIMIT_ERROR"
    CONFLICT_ERROR = "CONFLICT_ERROR"
    BAD_REQUEST = "BAD_REQUEST"
