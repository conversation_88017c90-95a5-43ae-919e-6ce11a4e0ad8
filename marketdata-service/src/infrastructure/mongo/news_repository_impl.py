import asyncio
import logging
from typing import List, Optional
from motor.motor_asyncio import AsyncIOMotorDatabase
from pymongo import DESCENDING, UpdateOne
from zoneinfo import ZoneInfo
from datetime import datetime
from core.entities.news_article import NewsArticle
from core.repositories.news_repository import NewsRepository
from core.exceptions import RepositoryError


logger = logging.getLogger(__name__)


class MongoNewsRepository(NewsRepository):
    def __init__(self, db: AsyncIOMotorDatabase) -> None:
        self._db = db
        self._coll_name = "news_articles"
        self._init_lock = asyncio.Lock()
        self._ready = False

    async def _ensure_collection(self) -> None:
        """Ensure collection exists and indexes are created."""
        if self._ready:
            return
        async with self._init_lock:
            if self._ready:
                return
            # Create indexes for efficient queries
            coll = self._db[self._coll_name]
            await coll.create_index("link", unique=True)
            await coll.create_index("source")
            await coll.create_index([("published_at", DESCENDING)])
            await coll.create_index([("source", 1), ("published_at", DESCENDING)])
            # Text index for keyword search
            await coll.create_index([("title", "text"), ("description", "text")])
            self._ready = True

    def _to_document(self, article: NewsArticle) -> dict:
        """Convert NewsArticle entity to MongoDB document."""
        return {
            "title": article.title,
            "link": article.link,
            "description": article.description,
            "published_at": article.published_at,
            "source": article.source,
            "source_name": article.source_name,
            "categories": article.categories or [],
            "image_url": article.image_url,
            "guid": article.guid,
        }

    def _from_document(self, doc: dict) -> NewsArticle:
        # Extract and normalize published_at
        published_at = doc.get("published_at")

        # Handle string, datetime, or missing cases gracefully
        if isinstance(published_at, str):
            try:
                # Try to parse the string to datetime
                published_at = datetime.fromisoformat(published_at)
            except Exception:
                try:
                    # Fallback: more flexible parse
                    from dateutil.parser import parse as dateutil_parse

                    published_at = dateutil_parse(published_at)
                except Exception:
                    logger.warning(f"Could not parse published_at string: {published_at}")
                    published_at = datetime.now()

        if isinstance(published_at, datetime):
            # If it’s timezone-aware → convert to Bangkok
            if published_at.tzinfo is not None:
                published_at = published_at.astimezone(ZoneInfo("Asia/Bangkok"))
            else:
                # If naive → assume it’s UTC, then convert to Bangkok
                published_at = published_at.replace(tzinfo=ZoneInfo("UTC")).astimezone(
                    ZoneInfo("Asia/Bangkok")
                )
        else:
            # If it's missing or invalid, fallback to now in Bangkok
            published_at = datetime.now(ZoneInfo("Asia/Bangkok"))

        # Return normalized NewsArticle
        return NewsArticle(
            title=doc["title"],
            link=doc["link"],
            description=doc["description"],
            published_at=published_at,
            source=doc["source"],
            source_name=doc["source_name"],
            categories=doc.get("categories", []),
            image_url=doc.get("image_url"),
            guid=doc.get("guid"),
        )

    async def save_article(self, article: NewsArticle) -> None:
        await self._ensure_collection()
        try:
            doc = self._to_document(article)
            await self._db[self._coll_name].update_one(
                {"link": article.link}, {"$set": doc}, upsert=True
            )
        except Exception as exc:
            raise RepositoryError(f"Failed to save article: {exc}") from exc

    async def save_articles(self, articles: List[NewsArticle]) -> int:
        await self._ensure_collection()
        if not articles:
            return 0

        try:
            operations = []
            for article in articles:
                doc = self._to_document(article)
                operations.append(UpdateOne({"link": article.link}, {"$set": doc}, upsert=True))

            if operations:
                result = await self._db[self._coll_name].bulk_write(operations, ordered=False)
                return result.upserted_count + result.modified_count
            return 0
        except Exception as exc:
            logger.error(f"Failed to save articles: {exc}")
            raise RepositoryError(f"Failed to save articles: {exc}") from exc

    async def get_by_link(self, link: str) -> Optional[NewsArticle]:
        """Retrieve a news article by its link URL."""
        await self._ensure_collection()
        try:
            doc = await self._db[self._coll_name].find_one({"link": link})
            return self._from_document(doc) if doc else None
        except Exception as exc:
            raise RepositoryError(f"Failed to get article by link: {exc}") from exc

    async def get_by_source(
        self, source: str, limit: int = 100, skip: int = 0
    ) -> List[NewsArticle]:
        """Retrieve news articles from a specific source."""
        await self._ensure_collection()
        try:
            cursor = (
                self._db[self._coll_name]
                .find({"source": source})
                .sort("published_at", DESCENDING)
                .skip(skip)
                .limit(limit)
            )
            results = []
            async for doc in cursor:
                results.append(self._from_document(doc))
            return results
        except Exception as exc:
            raise RepositoryError(f"Failed to get articles by source: {exc}") from exc

    async def get_recent(self, limit: int = 100, skip: int = 0) -> tuple[List[NewsArticle], int]:
        """Retrieve recent news articles sorted by publication date."""
        await self._ensure_collection()
        try:
            total = await self._db[self._coll_name].count_documents({})
            cursor = (
                self._db[self._coll_name]
                .find()
                .sort("published_at", DESCENDING)
                .skip(skip)
                .limit(limit)
            )
            results = []
            async for doc in cursor:
                results.append(self._from_document(doc))
            return results, total
        except Exception as exc:
            raise RepositoryError(f"Failed to get recent articles: {exc}") from exc

    async def filter_source_recent(
        self, limit: int = 100, skip: int = 0, source: str = None
    ) -> tuple[List[NewsArticle], int]:
        await self._ensure_collection()

        try:
            query = {}
            if source:
                query["source_name"] = source

            total = await self._db[self._coll_name].count_documents(query)

            cursor = (
                self._db[self._coll_name]
                .find(query)
                .sort("published_at", DESCENDING)
                .skip(skip)
                .limit(limit)
            )

            results = []
            async for doc in cursor:
                results.append(self._from_document(doc))

            return results, total
        except Exception as exc:
            raise RepositoryError(f"Failed to get recent articles: {exc}") from exc

    async def get_by_date_range(
        self, start: datetime, end: datetime, limit: int = 1000
    ) -> List[NewsArticle]:
        """Retrieve news articles within a date range."""
        await self._ensure_collection()
        try:
            cursor = (
                self._db[self._coll_name]
                .find({"published_at": {"$gte": start, "$lte": end}})
                .sort("published_at", DESCENDING)
                .limit(limit)
            )
            results = []
            async for doc in cursor:
                results.append(self._from_document(doc))
            return results
        except Exception as exc:
            raise RepositoryError(f"Failed to get articles by date range: {exc}") from exc

    async def search_by_keyword(
        self, keyword: str, limit: int = 100, skip: int = 0
    ) -> List[NewsArticle]:
        await self._ensure_collection()
        try:
            cursor = (
                self._db[self._coll_name]
                .find({"$text": {"$search": keyword}})
                .sort("published_at", DESCENDING)
                .skip(skip)
                .limit(limit)
            )
            results = []
            async for doc in cursor:
                results.append(self._from_document(doc))
            return results
        except Exception as exc:
            raise RepositoryError(f"Failed to search articles: {exc}") from exc

    async def exists_by_link(self, link: str) -> bool:
        """Check if a news article exists by its link."""
        await self._ensure_collection()
        try:
            count = await self._db[self._coll_name].count_documents({"link": link}, limit=1)
            return count > 0
        except Exception as exc:
            raise RepositoryError(f"Failed to check article existence: {exc}") from exc

    async def delete_old_articles(self, before: datetime) -> int:
        """Delete news articles published before a specific date."""
        await self._ensure_collection()
        try:
            result = await self._db[self._coll_name].delete_many({"published_at": {"$lt": before}})
            return result.deleted_count
        except Exception as exc:
            raise RepositoryError(f"Failed to delete old articles: {exc}") from exc

    async def count_by_source(self, source: str) -> int:
        """Count news articles from a specific source."""
        await self._ensure_collection()
        try:
            count = await self._db[self._coll_name].count_documents({"source": source})
            return count
        except Exception as exc:
            raise RepositoryError(f"Failed to count articles: {exc}") from exc
