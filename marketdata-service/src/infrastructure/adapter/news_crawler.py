import asyncio
import json
import logging
from pathlib import Path
from datetime import datetime
from typing import Optional, List

from src.core.usecases.crawl_news import Crawl<PERSON>ewsUseCase
from src.infrastructure.adapter.rss_crawler import RSSCrawler
from src.infrastructure.mongo.mongo_client import get_mongo_client
from src.infrastructure.mongo.news_repository_impl import MongoNewsRepository

logger = logging.getLogger(__name__)

class NewsCrawlerAdapter:
    def __init__(
        self,
        feeds_config_path: Optional[Path] = None,
        interval_minutes: int = 15,
        run_immediately: bool = True,
    ):
        self.feeds_config_path = Path(feeds_config_path or Path(__file__).parent.parent.parent / "rss_feeds_config.json")
        self.interval_seconds = max(1, interval_minutes) * 60
        self.run_immediately = run_immediately

        self._task: Optional[asyncio.Task] = None
        self._stop_event = asyncio.Event()

    async def _load_feeds(self) -> List[dict]:
        if not self.feeds_config_path.exists():
            logger.error("RSS feeds config file not found: %s", self.feeds_config_path)
            return []
        try:
            # Use async file read if you want; simple sync read is fine here.
            with open(self.feeds_config_path, "r", encoding="utf-8") as fh:
                config = json.load(fh)
            feeds = config.get("feeds", [])
            return feeds
        except Exception:
            logger.exception("Failed to load feeds config")
            return []

    async def run_once(self) -> bool:
        """Run a single crawl cycle and return True on success."""
        feeds = await self._load_feeds()
        if not feeds:
            logger.warning("No feeds to crawl (feeds list empty)")
            return False

        logger.info("Starting crawl cycle at %s (feeds=%d)", datetime.utcnow().isoformat(), len(feeds))

        try:
            # initialize dependencies here so tests can patch get_mongo_client if needed
            client = get_mongo_client()
            db = client["market_data"]
            repo = MongoNewsRepository(db)

            # configure RSSCrawler with sensible defaults
            crawler = RSSCrawler(
                max_retries=3,
                retry_delay=1.0,
                timeout=30,
                rate_limit_delay=1.0,
            )

            usecase = CrawlNewsUseCase(repo, crawler)
            result = await usecase.execute(feeds)

            # log summary
            logger.info("=" * 60)
            logger.info("CRAWL CYCLE COMPLETED")
            logger.info("Total feeds crawled: %s", result.get("total_feeds"))
            logger.info("Total articles fetched: %s", result.get("total_articles_fetched"))
            logger.info("Total articles saved: %s", result.get("total_articles_saved"))
            for source, stats in result.get("by_source", {}).items():
                logger.info(
                    "  %s: status=%s fetched=%s saved=%s",
                    source,
                    stats.get("status"),
                    stats.get("fetched", 0),
                    stats.get("saved", 0),
                )
            logger.info("=" * 60)
            return True

        except Exception:
            logger.exception("Error while running crawl use case")
            return False

    async def _loop(self):
        """Internal loop: run once (optionally), then sleep until next run or stop."""
        if self.run_immediately:
            try:
                await self.run_once()
            except asyncio.CancelledError:
                logger.info("Crawler initial run cancelled")
                return
            except Exception:
                logger.exception("Unhandled exception during initial crawl run")

        while not self._stop_event.is_set():
            # Wait for either stop event or timeout
            try:
                await asyncio.wait_for(self._stop_event.wait(), timeout=self.interval_seconds)
            except asyncio.TimeoutError:
                # timeout => perform a run
                try:
                    await self.run_once()
                except asyncio.CancelledError:
                    logger.info("Crawler run cancelled")
                    break
                except Exception:
                    logger.exception("Unhandled exception during scheduled crawl")
                    # continue to next interval

    def start(self, loop=None):
        """Start the background loop as an asyncio.Task."""
        if self._task and not self._task.done():
            logger.debug("NewsCrawlerService already started")
            return
        self._stop_event.clear()
        self._task = asyncio.create_task(self._loop())
        logger.info("NewsCrawlerService started (interval=%s seconds)", self.interval_seconds)

    async def stop(self, wait: bool = True):
        """Stop the background loop and optionally wait for task to complete."""
        logger.info("Stopping NewsCrawlerService...")
        self._stop_event.set()
        if self._task:
            if wait:
                try:
                    await self._task
                except Exception:
                    logger.exception("Error while waiting for crawler task to finish")
        logger.info("NewsCrawlerService stopped")
