from __future__ import annotations

from typing import Sequence
import pandas as pd
import talib as ta
import logging

logger = logging.getLogger(__name__)

class TechnicalAnalysisAdapter:
    _DEFAULT_INDICATORS: list[str] = [
        "sma",
        "ema",
        "rsi",
        "macd",
        "bbands",
        "atr",
        "adx",
        "stoch",
        "cci",
        "obv",
        "mfi",
        "roc",
        "williams_r",
        "cmo",
    ]

    _CANDLE_PATTERNS: list[str] = [
        "CDLDOJI",
        "CDLENGULFING",
        "CDLHAMMER",
        "CDLSHOOTINGSTAR",
        "CD<PERSON>ORNINGSTAR",
        "CDLEVENINGSTAR",
    ]

    def __init__(
        self,
        indicators: Sequence[str] | None = None,
        patterns: Sequence[str] | None = None,
    ):
        self.indicators = indicators or self._DEFAULT_INDICATORS
        self.patterns = patterns or self._CANDLE_PATTERNS

    def compute(self, ohlcv: pd.DataFrame) -> pd.DataFrame:
        """
        Enrich *ohlcv* DataFrame with TA-Lib indicators and candlestick patterns.

        Expected columns: ['time', 'open', 'high', 'low', 'close', 'volume']
        """
        if ohlcv is None or ohlcv.empty:
            logger.warning("Empty OHLCV data received for technical analysis.")
            return ohlcv

        df = ohlcv.copy()

        # Normalize column names (common with vnstock)
        df.columns = [c.lower() for c in df.columns]
        required_cols = {"open", "high", "low", "close", "volume"}
        if not required_cols.issubset(df.columns):
            raise ValueError(f"Missing required OHLCV columns: {required_cols - set(df.columns)}")

        open_, high, low, close, volume = (
            df["open"].astype(float).values,
            df["high"].astype(float).values,
            df["low"].astype(float).values,
            df["close"].astype(float).values,
            df["volume"].astype(float).values,
        )

        # --- Trend & momentum indicators -------------------------------------------------
        if "sma" in self.indicators:
            df["sma_20"] = ta.SMA(close, timeperiod=20)
        if "ema" in self.indicators:
            df["ema_20"] = ta.EMA(close, timeperiod=20)
        if "rsi" in self.indicators:
            df["rsi_14"] = ta.RSI(close, timeperiod=14)
        if "macd" in self.indicators:
            macd, macdsig, _ = ta.MACD(close)
            df["macd"] = macd
            df["macd_signal"] = macdsig
        if "bbands" in self.indicators:
            upper, middle, lower = ta.BBANDS(close)
            df["bb_high"] = upper
            df["bb_mid"] = middle
            df["bb_low"] = lower
        if "atr" in self.indicators:
            df["atr_14"] = ta.ATR(high, low, close, timeperiod=14)
        if "adx" in self.indicators:
            df["adx_14"] = ta.ADX(high, low, close, timeperiod=14)
        if "stoch" in self.indicators:
            k, d = ta.STOCH(high, low, close)
            df["stoch_%k"] = k
            df["stoch_%d"] = d
        if "cci" in self.indicators:
            df["cci_20"] = ta.CCI(high, low, close, timeperiod=20)
        if "obv" in self.indicators:
            df["obv"] = ta.OBV(close, volume)
        if "mfi" in self.indicators:
            df["mfi_14"] = ta.MFI(high, low, close, volume, timeperiod=14)
        if "roc" in self.indicators:
            df["roc_10"] = ta.ROC(close, timeperiod=10)
        if "williams_r" in self.indicators:
            df["williams_r_14"] = ta.WILLR(high, low, close, timeperiod=14)
        if "cmo" in self.indicators:
            df["cmo_14"] = ta.CMO(close, timeperiod=14)

        # --- Candlestick pattern recognition ---------------------------------------------
        for pattern in self.patterns:
            try:
                func = getattr(ta, pattern)
                df[pattern.lower()] = func(open_, high, low, close)
            except AttributeError:
                logger.warning(f"Unknown candlestick pattern: {pattern}")

        return df
