from vnstock import Quote
import pandas as pd

class VnStockAdapter:
    def __init__(self, source: str = "VCI"):
        self.source = source

    def get_history(self, symbol: str, start: str, end: str) -> pd.DataFrame:
        """
        Fetch historical data for a symbol.
        Expands range automatically if today/yesterday have no data.
        """
        quote = Quote(symbol=symbol, source=self.source)
        data = quote.history(start=start, end=end)

        if data is None or len(data) == 0:
            raise ValueError(f"No data returned for {symbol}")

        data = data.rename(columns=lambda x: x.strip().lower())
        data = data.sort_values("time").reset_index(drop=True)
        return data
