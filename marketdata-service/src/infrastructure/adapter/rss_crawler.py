import asyncio
import logging
from datetime import datetime, timezone
from typing import List, Optional, Dict
from urllib.parse import urlparse

import aiohttp
import feedparser
from bs4 import BeautifulSoup
from dateutil.parser import parse as dateutil_parse

from core.entities.news_article import NewsArticle
from core.exceptions import RepositoryError
from infrastructure.utils.text_processor import text_processor

logger = logging.getLogger(__name__)


class RSSCrawler:
    def __init__(
        self,
        max_retries: int = 3,
        retry_delay: float = 1.0,
        timeout: int = 30,
        rate_limit_delay: float = 1.0,
    ) -> None:
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.timeout = timeout
        self.rate_limit_delay = rate_limit_delay
        self._session: Optional[aiohttp.ClientSession] = None
        self._last_request_time: Dict[str, float] = {}
        self.text_processor = text_processor

    async def _get_session(self) -> aiohttp.ClientSession:
        if self._session is None or self._session.closed:
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            self._session = aiohttp.ClientSession(
                timeout=timeout,
                headers={
                    "User-Agent": "Mozilla/5.0 (compatible; NewsBot/1.0; +http://example.com/bot)"
                },
            )
        return self._session

    async def close(self) -> None:
        if self._session and not self._session.closed:
            await self._session.close()
            self._session = None

    async def _rate_limit(self, url: str) -> None:
        domain = urlparse(url).netloc
        if domain in self._last_request_time:
            elapsed = asyncio.get_event_loop().time() - self._last_request_time[domain]
            if elapsed < self.rate_limit_delay:
                await asyncio.sleep(self.rate_limit_delay - elapsed)
        self._last_request_time[domain] = asyncio.get_event_loop().time()

    async def _fetch_feed_content(self, url: str, source: str) -> Optional[str]:
        session = await self._get_session()

        for attempt in range(self.max_retries):
            try:
                await self._rate_limit(url)

                async with session.get(url) as response:
                    if response.status == 200:
                        content = await response.text()
                        return content
                    elif response.status == 429:  # Too Many Requests
                        logger.warning(f"Rate limited by {source}, waiting longer...")
                        await asyncio.sleep(self.retry_delay * (attempt + 2))
                    else:
                        logger.warning(f"Failed to fetch {source}: HTTP {response.status}")

            except asyncio.TimeoutError:
                logger.warning(f"Timeout fetching {source} (attempt {attempt + 1})")
            except aiohttp.ClientError as exc:
                logger.warning(f"Client error fetching {source}: {exc} (attempt {attempt + 1})")
            except Exception as exc:
                logger.error(f"Unexpected error fetching {source}: {exc}")

            if attempt < self.max_retries - 1:
                await asyncio.sleep(self.retry_delay * (attempt + 1))

        logger.error(f"Failed to fetch {source} after {self.max_retries} attempts")
        return None

    def _clean_html(self, html_content: str) -> str:
        if not html_content:
            return ""

        try:
            soup = BeautifulSoup(html_content, "lxml")
            for script in soup(["script", "style"]):
                script.decompose()
            text = soup.get_text()
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = " ".join(chunk for chunk in chunks if chunk)

            text = text_processor.clean_text(text)
            return text
        except Exception as exc:
            logger.warning(f"Failed to clean HTML: {exc}")
            return ""

    def _parse_date(self, entry: dict) -> datetime:
        if hasattr(entry, "published_parsed") and entry.published_parsed:
            try:
                time_tuple = entry.published_parsed
                if len(time_tuple) >= 6:
                    return datetime(*time_tuple[:6], tzinfo=timezone.utc)
                logger.debug(f"Invalid published_parsed tuple: {time_tuple}")
            except Exception as exc:
                logger.debug(f"Failed to parse published_parsed: {exc}")

        if hasattr(entry, "updated_parsed") and entry.updated_parsed:
            try:
                time_tuple = entry.updated_parsed
                if len(time_tuple) >= 6:
                    return datetime(*time_tuple[:6], tzinfo=timezone.utc)
                logger.debug(f"Invalid updated_parsed tuple: {time_tuple}")
            except Exception as exc:
                logger.debug(f"Failed to parse updated_parsed: {exc}")

        from email.utils import parsedate_to_datetime

        date_str = entry.get("published") or entry.get("updated")
        if date_str:
            try:
                return parsedate_to_datetime(date_str)
            except Exception as exc:
                logger.warning(f"Failed to parse date string '{date_str}': {exc}")
                try:
                    return dateutil_parse(date_str, fuzzy=True).replace(tzinfo=timezone.utc)
                except Exception as exc:
                    logger.warning(f"dateutil failed to parse date string '{date_str}': {exc}")

        logger.warning(
            f"No valid date found in entry from {entry.get('link', 'unknown')}, using current time"
        )
        return datetime.now(timezone.utc)

    def _extract_image_url(self, entry: dict) -> Optional[str]:
        if "media_content" in entry and entry["media_content"]:
            return entry["media_content"][0].get("url")

        if "media_thumbnail" in entry and entry["media_thumbnail"]:
            return entry["media_thumbnail"][0].get("url")

        if "enclosures" in entry and entry["enclosures"]:
            for enclosure in entry["enclosures"]:
                if enclosure.get("type", "").startswith("image/"):
                    return enclosure.get("href")

        html_content = entry.get("description") or entry.get("content", [{}])[0].get("value", "")
        if html_content:
            try:
                soup = BeautifulSoup(html_content, "lxml")
                img_tag = soup.find("img")
                if img_tag and img_tag.get("src"):
                    return img_tag["src"]
            except Exception as exc:
                logger.warning(f"Failed to extract image from HTML: {exc}")

        return None

    def _extract_description(self, entry: dict, source: str) -> str:
        """Extract description from RSS entry, trying multiple fields in order of preference."""
        import html

        description_candidates = []

        # Debug: Log all available fields in the entry
        logger.debug(f"[{source}] Available entry fields: {list(entry.keys())}")

        # Try content:encoded first (often has the most complete content)
        if hasattr(entry, "content") and entry.content:
            try:
                if isinstance(entry.content, list) and len(entry.content) > 0:
                    content_val = entry.content[0].get("value", "")
                    if content_val and content_val.strip():
                        # Decode HTML entities in case CDATA is HTML-encoded
                        content_val = html.unescape(content_val)
                        description_candidates.append(("content[0].value", content_val))
                        logger.debug(f"[{source}] Found content[0].value: {len(content_val)} chars")
            except Exception as e:
                logger.debug(f"[{source}] Error accessing entry.content: {e}")

        # Try summary field (common in many feeds)
        summary = entry.get("summary", "")
        if summary and summary.strip():
            summary = html.unescape(summary)
            description_candidates.append(("summary", summary))
            logger.debug(f"[{source}] Found summary: {len(summary)} chars")

        # Try description field (your feed has this)
        description = entry.get("description", "")
        if description and description.strip():
            # This is the key fix - decode HTML entities for CDATA content
            description = html.unescape(description)
            description_candidates.append(("description", description))
            logger.debug(f"[{source}] Found description: {len(description)} chars")

        # Try summary_detail field (sometimes contains more info)
        if hasattr(entry, "summary_detail") and entry.summary_detail:
            summary_detail_value = entry.summary_detail.get("value", "")
            if summary_detail_value and summary_detail_value.strip():
                summary_detail_value = html.unescape(summary_detail_value)
                description_candidates.append(("summary_detail.value", summary_detail_value))
                logger.debug(
                    f"[{source}] Found summary_detail.value: {len(summary_detail_value)} chars"
                )

        # Try any field that might contain encoded content
        for key in entry.keys():
            if "content" in key.lower() and key not in ["content", "content_detail"]:
                value = entry.get(key, "")
                if isinstance(value, str) and value.strip():
                    value = html.unescape(value)
                    description_candidates.append((key, value))
                    logger.debug(f"[{source}] Found {key}: {len(value)} chars")

        # Log what we found
        if description_candidates:
            # Return the longest non-empty description
            best_candidate = max(description_candidates, key=lambda x: len(x[1]) if x[1] else 0)
            return best_candidate[1]
        else:
            title = entry.get("title", "")
            if title and title.strip():
                return title
            return ""

    def _extract_descriptions_from_raw_content(self, content: str, source: str) -> Dict[str, str]:
        """Extract descriptions from raw RSS content for entries that feedparser can't parse properly."""
        import re
        import html

        descriptions = {}

        # Find all items in the RSS content
        item_pattern = r"<item>(.*?)</item>"
        items = re.findall(item_pattern, content, re.DOTALL)

        for item in items:
            # Extract link to use as identifier
            link_match = re.search(r"<link>(.*?)</link>", item)
            if not link_match:
                continue
            link = link_match.group(1).strip()

            # Extract description with CDATA
            desc_pattern = r"<description>(.*?)</description>"
            desc_match = re.search(desc_pattern, item, re.DOTALL)
            if desc_match:
                raw_desc = desc_match.group(1).strip()
                decoded_desc = html.unescape(raw_desc)
                descriptions[link] = decoded_desc
                logger.debug(
                    f"[{source}] Extracted description for {link}: {len(decoded_desc)} chars"
                )

        return descriptions

    async def crawl_feed(self, feed_url: str, source: str, source_name: str) -> List[NewsArticle]:
        content = await self._fetch_feed_content(feed_url, source)
        if not content:
            logger.warning(f"No content fetched from {source}")
            return []

        try:
            # Extract descriptions from raw content as fallback
            raw_descriptions = self._extract_descriptions_from_raw_content(content, source)

            feed = feedparser.parse(content)

            if feed.bozo and feed.bozo_exception:
                logger.warning(f"Feed parsing warning for {source}: {feed.bozo_exception}")

            articles = []
            for entry in feed.entries:
                try:
                    raw_title = entry.get("title", "").strip()
                    title = text_processor.clean_title(raw_title)
                    link = entry.get("link", "").strip()

                    raw_description = self._extract_description(entry, source)

                    # If no description found via feedparser, try raw content extraction
                    if (
                        not raw_description
                        or len(raw_description.strip()) == 0
                        or raw_description == title
                    ):
                        link = entry.get("link", "").strip()
                        if link in raw_descriptions:
                            raw_description = raw_descriptions[link]
                        else:
                            logger.debug(f"[{source}] Link {link} not found in raw descriptions")
                            logger.debug(
                                f"[{source}] Available links: {list(raw_descriptions.keys())[:3]}..."
                            )

                    # Clean the description using text processor
                    if raw_description and len(raw_description.strip()) > 0:
                        raw_description = self.text_processor.clean_description(raw_description)

                    description = text_processor.clean_description(
                        self._clean_html(raw_description)
                    )

                    if not title or not link:
                        logger.debug(f"Skipping entry without title or link in {source}")
                        continue

                    published_at = self._parse_date(entry)
                    categories = [
                        text_processor.clean_text(tag.get("term", ""))
                        for tag in entry.get("tags", [])
                    ]

                    image_url = self._extract_image_url(entry)
                    guid = entry.get("id") or entry.get("guid")

                    article = NewsArticle(
                        title=title,
                        link=link,
                        description=description,
                        published_at=published_at,
                        source=source,
                        source_name=source_name,
                        categories=categories,
                        image_url=image_url,
                        guid=guid,
                    )
                    articles.append(article)
                    logger.debug(f"Parsed article from {source}: {title[:50]}... (link: {link})")

                except ValueError as exc:
                    logger.warning(
                        f"Invalid article data in {source} (title: {title[:50] if title else 'N/A'}, link: {link}): {exc}"
                    )
                except Exception as exc:
                    logger.error(
                        f"Error parsing entry in {source} (title: {title[:50] if 'title' in locals() else 'N/A'}, link: {link}): {exc}",
                        exc_info=True,
                    )

            return articles

        except Exception as exc:
            logger.error(f"Failed to parse feed from {source}: {exc}")
            raise RepositoryError(f"Failed to parse feed from {source}: {exc}") from exc

    async def crawl_multiple_feeds(
        self, feeds: List[Dict[str, str]]
    ) -> Dict[str, List[NewsArticle]]:
        logger.info(f"Crawling {len(feeds)} RSS feeds")

        tasks = [
            self.crawl_feed(feed["url"], feed["source"], feed["source_name"]) for feed in feeds
        ]

        results = await asyncio.gather(*tasks, return_exceptions=True)

        articles_by_source = {}
        for feed, result in zip(feeds, results):
            source = feed["source"]
            if isinstance(result, Exception):
                logger.error(f"Failed to crawl {source}: {result}")
                articles_by_source[source] = []
            else:
                articles_by_source[source] = result

        total_articles = sum(len(articles) for articles in articles_by_source.values())
        logger.info(f"Crawled {total_articles} total articles from {len(feeds)} feeds")

        return articles_by_source
