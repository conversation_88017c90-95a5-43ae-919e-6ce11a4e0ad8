import html
import re
import unicodedata
from typing import Optional
import logging

logger = logging.getLogger(__name__)


class TextProcessor:
    def __init__(self):
        self._html_entities = {
            "&amp;": "&",
            "&lt;": "<",
            "&gt;": ">",
            "&quot;": '"',
            "&apos;": "'",
            "&nbsp;": " ",
            "&ndash;": "–",
            "&mdash;": "—",
            "&hellip;": "…",
            "&lsquo;": """,
            '&rsquo;': """,
            "&ldquo;": '"',
            "&rdquo;": '"',
            # Vietnamese named entities
            "&iacute;": "í",
            "&aacute;": "á",
            "&agrave;": "à",
            "&acirc;": "â",
            "&atilde;": "ã",
            "&eacute;": "é",
            "&egrave;": "è",
            "&ecirc;": "ê",
            "&iacute;": "í",
            "&igrave;": "ì",
            "&oacute;": "ó",
            "&ograve;": "ò",
            "&ocirc;": "ô",
            "&otilde;": "õ",
            "&uacute;": "ú",
            "&ugrave;": "ù",
            "&yacute;": "ý",
        }

        # Vietnamese character mappings for common encoding issues
        self._vietnamese_entities = {
            "&#224;": "à",  # à
            "&#225;": "á",  # á
            "&#226;": "â",  # â
            "&#227;": "ã",  # ã
            "&#228;": "ä",  # ä
            "&#232;": "è",  # è
            "&#233;": "é",  # é
            "&#234;": "ê",  # ê
            "&#235;": "ë",  # ë
            "&#236;": "ì",  # ì
            "&#237;": "í",  # í
            "&#238;": "î",  # î
            "&#239;": "ï",  # ï
            "&#242;": "ò",  # ò
            "&#243;": "ó",  # ó
            "&#244;": "ô",  # ô
            "&#245;": "õ",  # õ
            "&#246;": "ö",  # ö
            "&#249;": "ù",  # ù
            "&#250;": "ú",  # ú
            "&#251;": "û",  # û
            "&#252;": "ü",  # ü
            "&#253;": "ý",  # ý
            "&#255;": "ÿ",  # ÿ
            "&#192;": "À",  # À
            "&#193;": "Á",  # Á
            "&#194;": "Â",  # Â
            "&#195;": "Ã",  # Ã
            "&#196;": "Ä",  # Ä
            "&#200;": "È",  # È
            "&#201;": "É",  # É
            "&#202;": "Ê",  # Ê
            "&#203;": "Ë",  # Ë
            "&#204;": "Ì",  # Ì
            "&#205;": "Í",  # Í
            "&#206;": "Î",  # Î
            "&#207;": "Ï",  # Ï
            "&#210;": "Ò",  # Ò
            "&#211;": "Ó",  # Ó
            "&#212;": "Ô",  # Ô
            "&#213;": "Õ",  # Õ
            "&#214;": "Ö",  # Ö
            "&#217;": "Ù",  # Ù
            "&#218;": "Ú",  # Ú
            "&#219;": "Û",  # Û
            "&#220;": "Ü",  # Ü
            "&#221;": "Ý",  # Ý
            "&#259;": "ă",  # ă
            "&#258;": "Ă",  # Ă
            "&#273;": "đ",  # đ
            "&#272;": "Đ",  # Đ
            # Vietnamese tone marks
            "&#7871;": "ệ",  # ệ
            "&#7869;": "ề",  # ề
            "&#7873;": "ể",  # ể
            "&#7875;": "ễ",  # ễ
            "&#7877;": "ệ",  # ệ (duplicate, but keeping for safety)
            "&#7879;": "ị",  # ị
            "&#7881;": "ọ",  # ọ
            "&#7883;": "ồ",  # ồ
            "&#7885;": "ổ",  # ổ
            "&#7887;": "ỗ",  # ỗ
            "&#7889;": "ộ",  # ộ
            "&#7891;": "ờ",  # ờ
            "&#7893;": "ở",  # ở
            "&#7895;": "ỡ",  # ỡ
            "&#7897;": "ợ",  # ợ
            "&#7909;": "ụ",  # ụ
            "&#7911;": "ừ",  # ừ
            "&#7913;": "ử",  # ử
            "&#7915;": "ữ",  # ữ
            "&#7917;": "ự",  # ự
            "&#7923;": "ỳ",  # ỳ
            "&#7925;": "ỵ",  # ỵ
            "&#7927;": "ỷ",  # ỷ
            "&#7929;": "ỹ",  # ỹ
            # Additional Vietnamese characters
            "&#7843;": "ả",  # ả
            "&#7845;": "ấ",  # ấ
            "&#7847;": "ầ",  # ầ
            "&#7849;": "ẩ",  # ẩ
            "&#7851;": "ẫ",  # ẫ
            "&#7853;": "ậ",  # ậ
            "&#7855;": "ắ",  # ắ
            "&#7857;": "ằ",  # ằ
            "&#7859;": "ẳ",  # ẳ
            "&#7861;": "ẵ",  # ẵ
            "&#7863;": "ặ",  # ặ
            "&#7865;": "ẻ",  # ẻ
            "&#7867;": "ẽ",  # ẽ
            "&#7899;": "ỏ",  # ỏ
            "&#7901;": "ố",  # ố
            "&#7903;": "ổ",  # ổ (duplicate)
            "&#7905;": "ỗ",  # ỗ (duplicate)
            "&#7907;": "ộ",  # ộ (duplicate)
            "&#7919;": "ủ",  # ủ
            "&#7921;": "ứ",  # ứ
        }

    def decode_html_entities(self, text: str) -> str:
        if not text:
            return text

        try:
            text = re.sub(r"(?<!&)#(\d+);", r"&#\1;", text)

            # First, use Python's built-in HTML entity decoder
            decoded = html.unescape(text)

            # Handle custom Vietnamese entities
            for entity, char in self._vietnamese_entities.items():
                decoded = decoded.replace(entity, char)

            # Handle additional common HTML entities
            for entity, char in self._html_entities.items():
                decoded = decoded.replace(entity, char)

            # Handle numeric HTML entities that might be missed
            # Pattern for &#number; format
            def replace_numeric_entity(match):
                try:
                    num = int(match.group(1))
                    return chr(num)
                except (ValueError, OverflowError):
                    return match.group(0)  # Return original if can't convert

            decoded = re.sub(r"&#(\d+);", replace_numeric_entity, decoded)

            # Handle hexadecimal HTML entities &#xHEX;
            def replace_hex_entity(match):
                try:
                    num = int(match.group(1), 16)
                    return chr(num)
                except (ValueError, OverflowError):
                    return match.group(0)  # Return original if can't convert

            decoded = re.sub(r"&#x([0-9a-fA-F]+);", replace_hex_entity, decoded)

            return decoded

        except Exception as exc:
            logger.warning(f"Failed to decode HTML entities: {exc}")
            return text

    def normalize_whitespace(self, text: str) -> str:
        if not text:
            return text

        # Replace multiple whitespace characters with single space
        text = re.sub(r"\s+", " ", text)

        # Remove leading and trailing whitespace
        text = text.strip()

        return text

    def remove_unwanted_characters(self, text: str) -> str:
        if not text:
            return text

        # Remove control characters except for common ones like newline, tab
        text = "".join(
            char for char in text if unicodedata.category(char)[0] != "C" or char in "\n\t\r"
        )

        # Remove zero-width characters
        text = re.sub(r"[\u200b-\u200d\ufeff]", "", text)

        return text

    def clean_text(self, text: str) -> str:
        if not text:
            return ""

        try:
            # Step 1: Decode HTML entities
            cleaned = self.decode_html_entities(text)

            # Step 2: Remove unwanted characters
            cleaned = self.remove_unwanted_characters(cleaned)

            # Step 3: Normalize whitespace
            cleaned = self.normalize_whitespace(cleaned)

            return cleaned

        except Exception as exc:
            logger.warning(f"Failed to clean text: {exc}")
            return text

    def clean_title(self, title: str) -> str:
        if not title:
            return ""

        cleaned = self.clean_text(title)

        # Remove common title prefixes/suffixes that might be added by RSS feeds
        # This can be customized based on your specific RSS sources
        prefixes_to_remove = [
            r"^.*?:\s*",  # Remove "Source: " prefix
            r"^\[.*?\]\s*",  # Remove "[Category] " prefix
        ]

        for pattern in prefixes_to_remove:
            cleaned = re.sub(pattern, "", cleaned, flags=re.IGNORECASE)

        return cleaned.strip()

    def clean_description(self, description: str) -> str:
        if not description:
            return ""

        cleaned = self.clean_text(description)

        # Remove common description artifacts
        # Remove "Read more" links and similar
        patterns_to_remove = [
            r"\s*\.\.\.\s*$",  # Remove trailing "..."
            r"\s*\[.*?\]\s*$",  # Remove trailing "[Read more]" etc.
            r"\s*\(.*?\)\s*$",  # Remove trailing parenthetical notes
        ]

        for pattern in patterns_to_remove:
            cleaned = re.sub(pattern, "", cleaned, flags=re.IGNORECASE)

        return cleaned.strip()


text_processor = TextProcessor()
