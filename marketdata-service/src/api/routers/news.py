from typing import Optional
from fastapi import <PERSON>Router, Depends, Query, HTTPException
from datetime import datetime

from api.schemas import NewsArticleResponse
from core.usecases.manage_news import (
    GetRecentNewsUseCase,
    GetNewsBySourceUseCase,
    GetNewsByDateRangeUseCase,
    SearchNewsUseCase,
)

from src.common.dependencies import get_news_repository
from src.common.utils.response_utils import PaginationHelper
from src.common.utils.cache_utils import CacheUtils

router = APIRouter(prefix="/api/news", tags=["News"])

cache = CacheUtils()
CACHE_TTL_SECONDS = 200


@router.get("/recent")
async def get_recent_news(
    limit: int = Query(50, ge=1, le=1000, description="Maximum number of articles"),
    skip: int = Query(0, ge=0, description="Number of articles to skip"),
    source: Optional[str] = Query(None, description="Filter by news source"),
    repo=Depends(get_news_repository),
):
    """Get recent news with caching."""
    cache_key = f"news:recent:{source or 'all'}:{limit}:{skip}"

    async def fetch_recent_news():
        usecase = GetRecentNewsUseCase(repo)
        articles, total_count = await usecase.execute(limit, skip, source)
        article_responses = [NewsArticleResponse.model_validate(a) for a in articles]
        page, page_size = PaginationHelper.calculate_pagination_params(limit, skip, total_count)
        response = PaginationHelper.create_paginated_response(
            data=article_responses, total_count=total_count, page=page, page_size=page_size
        )
        return response.model_dump()

    try:
        data, cached = await cache.get_or_set(cache_key, fetch_recent_news, ttl=CACHE_TTL_SECONDS)
        return data
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/source/{source}")
async def get_news_by_source(
    source: str,
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of articles"),
    skip: int = Query(0, ge=0, description="Number of articles to skip"),
    repo=Depends(get_news_repository),
):
    """Get news articles by source with pagination and caching."""
    cache_key = f"news:source:{source}:{limit}:{skip}"

    async def fetch_by_source():
        usecase = GetNewsBySourceUseCase(repo)
        articles = await usecase.execute(source, limit, skip)
        article_responses = [NewsArticleResponse.model_validate(a) for a in articles]
        total_count = len(article_responses)
        page, page_size = PaginationHelper.calculate_pagination_params(limit, skip, total_count)
        response = PaginationHelper.create_paginated_response(
            data=article_responses, total_count=total_count, page=page, page_size=page_size
        )
        return response.model_dump()

    try:
        data, cached = await cache.get_or_set(cache_key, fetch_by_source, ttl=CACHE_TTL_SECONDS)
        return data
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/range")
async def get_news_by_date_range(
    start: datetime = Query(..., description="Start of date range"),
    end: datetime = Query(..., description="End of date range"),
    limit: int = Query(1000, ge=1, le=10000, description="Maximum number of articles"),
    repo=Depends(get_news_repository),
):
    """Get news articles by date range (cached)."""
    cache_key = f"news:range:{start.isoformat()}:{end.isoformat()}:{limit}"

    async def fetch_by_date_range():
        usecase = GetNewsByDateRangeUseCase(repo)
        articles = await usecase.execute(start, end, limit)
        article_responses = [NewsArticleResponse.model_validate(a) for a in articles]
        response = PaginationHelper.create_paginated_response(
            data=article_responses,
            total_count=len(article_responses),
            page=1,
            page_size=len(article_responses),
        )
        return response.model_dump()

    try:
        data, cached = await cache.get_or_set(cache_key, fetch_by_date_range, ttl=CACHE_TTL_SECONDS)
        return data
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/search")
async def search_news(
    q: str = Query(..., min_length=1, description="Search keyword"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of articles"),
    skip: int = Query(0, ge=0, description="Number of articles to skip"),
    repo=Depends(get_news_repository),
):
    """Search news articles with caching."""
    cache_key = f"news:search:{q}:{limit}:{skip}"

    async def fetch_search_results():
        usecase = SearchNewsUseCase(repo)
        articles = await usecase.execute(q, limit, skip)
        article_responses = [NewsArticleResponse.model_validate(a) for a in articles]
        total_count = len(article_responses)
        page, page_size = PaginationHelper.calculate_pagination_params(limit, skip, total_count)
        response = PaginationHelper.create_paginated_response(
            data=article_responses, total_count=total_count, page=page, page_size=page_size
        )
        return response.model_dump()

    try:
        data, cached = await cache.get_or_set(cache_key, fetch_search_results, ttl=CACHE_TTL_SECONDS)
        return data
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
