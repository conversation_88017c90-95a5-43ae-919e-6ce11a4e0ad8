import logging

from fastapi import APIRouter, Query

from src.common.common_function import CommonFunction
from src.common.utils.response_utils import create_success_response
from src.core.usecases.manage_symbols import GetSymbolTechnicalAnalysisUseCase

router = APIRouter(prefix="/api", tags=["Symbols"])
logging = logging.getLogger(__name__)

@router.get("/symbols/{symbol}/technical-analysis")
def get_symbol_technical_analysis(
    symbol: str,
    start_date: str = Query(..., description="Start date in YYYY-MM-DD format"),
    end_date: str = Query(..., description="End date in YYYY-MM-DD format"),
):
    use_case = GetSymbolTechnicalAnalysisUseCase()
    df_result = use_case.execute(symbol, start_date, end_date)
    records = CommonFunction.dataframe_to_records(df_result)
    return create_success_response(data=records)
