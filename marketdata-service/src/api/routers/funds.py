from fastapi import APIRouter, Query, HTTPException
from typing import Optional
from vnstock import Fund

from src.common.utils.cache_utils import CacheUtils
from src.common.utils.response_utils import create_success_response

router = APIRouter(prefix="/api", tags=["Funds"])
fund = Fund()
cache = CacheUtils()

CACHE_TTL_SECONDS = 6000


@router.get("/funds/listing")
async def get_funds_listing(
    fund_type: Optional[str] = Query(
        default="", description="Type of fund: BALANCED, BOND, STOCK, or empty for all"
    )
):
    try:
        cache_key = f"funds:listing:{fund_type or 'all'}"

        async def fetch_funds():
            df = fund.listing(fund_type=fund_type)
            return df.to_dict(orient="records")

        data, cached = await cache.get_or_set(cache_key, fetch_funds, ttl=CACHE_TTL_SECONDS)
        return create_success_response(data=data, message="cached" if cached else "fresh")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/funds/filter")
async def filter_fund(symbol: str = Query(..., description="Fund short name (e.g., SSISCA)")):
    """Filter fund info by short name"""
    try:
        cache_key = f"funds:listing:{symbol}/filter"

        async def fetch_filter():
            df = fund.filter(symbol=symbol)
            return df.to_dict(orient="records")

        data, cached = await cache.get_or_set(cache_key, fetch_filter, ttl=CACHE_TTL_SECONDS)
        return create_success_response(data=data, message="cached" if cached else "fresh")
    except Exception as e:
        raise HTTPException(status_code=404, detail=str(e))


@router.get("/funds/{symbol}/top-holding")
async def get_top_holding(symbol: str):
    try:
        cache_key = f"funds:details:{symbol}:top-holding"

        async def fetch_top_holding():
            df = fund.details.top_holding(symbol)
            return df.to_dict(orient="records")

        data, cached = await cache.get_or_set(cache_key, fetch_top_holding, ttl=CACHE_TTL_SECONDS)
        return create_success_response(data=data, message="cached" if cached else "fresh")
    except Exception as e:
        raise HTTPException(status_code=404, detail=str(e))


@router.get("/funds/{symbol}/industry-holding")
async def get_industry_holding(symbol: str):
    """Get top industries held in a specific fund"""
    try:
        cache_key = f"funds:details:{symbol}:industry-holding"

        async def fetch_industry_holding():
            df = fund.details.industry_holding(symbol)
            return df.to_dict(orient="records")

        data, cached = await cache.get_or_set(
            cache_key, fetch_industry_holding, ttl=CACHE_TTL_SECONDS
        )
        return create_success_response(data=data, message="cached" if cached else "fresh")
    except Exception as e:
        raise HTTPException(status_code=404, detail=str(e))


@router.get("/funds/{symbol}/nav-report")
async def get_nav_report(symbol: str):
    """Retrieve NAV history of a specific fund"""
    try:
        cache_key = f"funds:details:{symbol}:nav-report"

        async def fetch_nav_report():
            df = fund.details.nav_report(symbol)
            return df.to_dict(orient="records")

        data, cached = await cache.get_or_set(cache_key, fetch_nav_report, ttl=CACHE_TTL_SECONDS)
        return create_success_response(data=data, message="cached" if cached else "fresh")
    except Exception as e:
        raise HTTPException(status_code=404, detail=str(e))


@router.get("/funds/{symbol}/asset-holding")
async def get_asset_holding(symbol: str):
    """Retrieve asset allocation of a specific fund"""
    try:
        cache_key = f"funds:details:{symbol}:asset-holding"

        async def fetch_asset_holding():
            df = fund.details.asset_holding(symbol)
            return df.to_dict(orient="records")

        data, cached = await cache.get_or_set(cache_key, fetch_asset_holding, ttl=CACHE_TTL_SECONDS)
        return create_success_response(data=data, message="cached" if cached else "fresh")
    except Exception as e:
        raise HTTPException(status_code=404, detail=str(e))
