from datetime import date
from typing import Optional
from fastapi import APIRouter, Query, HTTPException
from vnstock.explorer.misc import *
from vnstock.explorer.misc.exchange_rate import *
from vnstock import Listing

from src.common.utils.response_utils import create_success_response

router = APIRouter(prefix="/api", tags=["Markets"])
listing = Listing(source="VCI")

@router.get("/golds/prices")
def get_gold_prices(
    query_date: Optional[date] = Query(
        default=None, description="Date to filter exchange rate (format: YYYY-MM-DD)"
    )
):
    try:
        gold_df = btmc_goldprice()

        return create_success_response(data=gold_df.to_dict(orient="records"))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/exchange-rate")
def get_gold_prices(
    query_date: Optional[str] = Query(
        default=None, description="Date to filter exchange rate (format: YYYY-MM-DD)"
    )
):
    try:
        exchange_rate_df = vcb_exchange_rate(date=query_date)

        return create_success_response(data=exchange_rate_df.to_dict(orient="records"))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/stocks/listing")
def get_stock_listing(group: Optional[str] = Query(default=None, description="Stock group name")):
    try:
        if group is None:
            stock_df = listing.all_symbols()
        else:
            stock_df = listing.symbols_by_group(group)

        return create_success_response(data=stock_df.to_dict(orient="records"))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
