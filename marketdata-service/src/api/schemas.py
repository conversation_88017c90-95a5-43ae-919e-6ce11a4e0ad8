from datetime import datetime
from typing import Optional, List, Dict

from pydantic import BaseModel, Field
from src.common.payload.response_models import StandardResponse, ListResponse, MessageResponse


class MarketIndicatorResponse(BaseModel):
    """Response schema for market indicator data."""

    symbol: str
    timestamp: datetime
    value: float

    class Config:
        from_attributes = True  # Pydantic v2 (was orm_mode in v1)


class TickerResponse(BaseModel):
    """Response schema for ticker data."""

    symbol: str = Field(..., description="Stock ticker symbol")
    name: str = Field(..., description="Full company name")
    short_name: str = Field(..., description="Abbreviated company name")
    floor: str = Field(..., description="Stock exchange floor (HOSE, HNX, UPCOM)")
    type: str = Field(..., description="Security type (STOCK, ETF, BOND)")

    class Config:
        from_attributes = True


class TickerCreate(BaseModel):
    """Request schema for creating a ticker."""

    symbol: str = Field(..., min_length=1, description="Stock ticker symbol")
    name: str = Field(..., min_length=1, description="Full company name")
    short_name: str = Field(..., min_length=1, description="Abbreviated company name")
    floor: str = Field(..., description="Stock exchange floor")
    type: str = Field(..., description="Security type")


class PriceResponse(BaseModel):
    """Response schema for price data."""

    symbol: str = Field(..., description="Stock ticker symbol")
    timestamp: datetime = Field(..., description="Time of the price data")
    open: float = Field(..., ge=0, description="Opening price")
    high: float = Field(..., ge=0, description="Highest price")
    low: float = Field(..., ge=0, description="Lowest price")
    close: float = Field(..., ge=0, description="Closing price")
    volume: int = Field(..., ge=0, description="Trading volume")
    adjusted_close: Optional[float] = Field(None, ge=0, description="Adjusted closing price")

    class Config:
        from_attributes = True


class NewsArticleResponse(BaseModel):
    """Response schema for news article data."""

    title: str = Field(..., description="Article title")
    link: str = Field(..., description="URL to the full article")
    description: str = Field(..., description="Article summary/description")
    published_at: datetime = Field(..., description="Publication date and time")
    source: str = Field(..., description="News source identifier")
    source_name: str = Field(..., description="Human-readable source name")
    categories: List[str] = Field(default_factory=list, description="Article categories/tags")
    image_url: Optional[str] = Field(None, description="URL to article's main image")
    guid: Optional[str] = Field(None, description="Unique identifier from RSS feed")

    class Config:
        from_attributes = True
        json_encoders = {datetime: lambda v: v.isoformat()}


class RSSFeedConfig(BaseModel):
    """Configuration for an RSS feed source."""

    url: str = Field(..., description="RSS feed URL")
    source: str = Field(..., description="Source identifier (e.g., 'vnexpress', 'cafef')")
    source_name: str = Field(..., description="Human-readable source name")


class CrawlNewsRequest(BaseModel):
    """Request schema for crawling news from RSS feeds."""

    feeds: List[RSSFeedConfig] = Field(..., description="List of RSS feeds to crawl")


class CrawlNewsResponse(BaseModel):
    """Response schema for news crawl operation."""

    total_feeds: int = Field(..., description="Number of feeds crawled")
    total_articles_fetched: int = Field(..., description="Total articles fetched from feeds")
    total_articles_saved: int = Field(..., description="Total articles saved to database")
    by_source: Dict[str, Dict] = Field(..., description="Results per source")


# Standardized response type aliases for this module
TickerListResponse = ListResponse[TickerResponse]
TickerSingleResponse = StandardResponse[TickerResponse]
PriceListResponse = ListResponse[PriceResponse]
PriceSingleResponse = StandardResponse[PriceResponse]
NewsListResponse = ListResponse[NewsArticleResponse]
NewsSingleResponse = StandardResponse[NewsArticleResponse]
CrawlNewsStandardResponse = StandardResponse[CrawlNewsResponse]
TickerCreateResponse = MessageResponse
