import logging
import os
from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
from pathlib import Path

from api.routers import funds, markets, news, symbols
from src.infrastructure.adapter.news_crawler import NewsCrawlerAdapter

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# configuration from env or defaults
CRAWL_INTERVAL_MINUTES = int(os.environ.get("CRAWL_INTERVAL_MINUTES", "15"))
FEEDS_CONFIG_PATH = os.environ.get("FEEDS_CONFIG_PATH", str(Path(__file__).parent.parent / "rss_feeds_config.json"))
RUN_CRAWLER = os.environ.get("RUN_CRAWLER", "true").lower() in ("1", "true", "yes")


@asynccontextmanager
async def lifespan(app: FastAPI):
    logger.info("🚀 Starting Market Data Service")

    # create and start crawler if enabled
    crawler_service = None
    if RUN_CRAWLER:
        crawler_service = NewsCrawlerAdapter(
            feeds_config_path=Path(FEEDS_CONFIG_PATH),
            interval_minutes=CRAWL_INTERVAL_MINUTES,
            run_immediately=True,
        )
        crawler_service.start()
        # attach to app state so other parts (or tests) can access it
        app.state.news_crawler = crawler_service
        logger.info("News crawler service created and started")

    try:
        yield
    finally:
        # shutdown
        if crawler_service:
            await crawler_service.stop()
        logger.info("🧩 Market Data Service shutting down")


def create_application() -> FastAPI:
    app = FastAPI(
        title="Market Data Service",
        description="Vietnamese stock market data and news service",
        version="1.0.0",
        lifespan=lifespan,
    )

    # CORS setup
    origins = ["*"]
    app.add_middleware(
        CORSMiddleware,
        allow_origins=origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Routers
    app.include_router(news.router)
    app.include_router(funds.router)
    app.include_router(markets.router)
    app.include_router(symbols.router)

    return app


app = create_application()
